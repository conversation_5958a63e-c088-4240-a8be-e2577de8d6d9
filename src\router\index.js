import { createRouter, createWebHistory } from 'vue-router'
import PublicView from '../pages/Public/PublicView.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'public',
      component: PublicView,
    },
    {
      path: '/login',
      name: 'login',
      component: () => import('../pages/Auth/LoginView.vue'),
    },
    {
      path: '/dashboard',
      name: 'dashboard',
      component: () => import('../pages/Dashboard/Dashboard.vue'),
    },
  ],
})

export default router
